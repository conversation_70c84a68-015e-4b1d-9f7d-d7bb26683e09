/**
 * User Behavior Simulation Classes
 * 
 * Simulates realistic user behavior patterns for tokenomics stress testing.
 * Each user type has distinct behavior patterns that trigger real game functions.
 */

import fetch from 'node-fetch';
import { ethers } from 'ethers';
import { GAME_CONFIG } from '../../src/config/gameConfig.js';
import { TokenEconomyManager } from '../../src/managers/TokenEconomyManager.js';
import { ETHTestModeManager } from '../../src/managers/ETHTestModeManager.js';
import { CircuitBreakerManager } from './CircuitBreaker.js';
import { FallbackSystem } from './FallbackSystem.js';

export class UserSimulator {
    constructor({ type, account, config, id, transactionTracker, treasuryMonitor = null, globalRateLimiter = null }) {
        this.type = type;
        this.account = account;
        this.config = config;
        this.id = id;
        this.transactionTracker = transactionTracker;
        this.treasuryMonitor = treasuryMonitor;
        this.globalRateLimiter = globalRateLimiter;

        // Set up blockchain provider and wallet
        this.provider = new ethers.JsonRpcProvider(config.hardhatUrl);

        // Use the wallet from the account object (created in TokenomicsStressTest)
        if (account.wallet) {
            this.wallet = account.wallet;
        } else if (account.privateKey) {
            this.wallet = new ethers.Wallet(account.privateKey, this.provider);
        } else {
            throw new Error(`Invalid account object for ${id}: missing wallet or privateKey`);
        }

        // Mock localStorage for Node.js environment
        if (typeof global !== 'undefined' && !global.localStorage) {
            global.localStorage = {
                getItem: () => null,
                setItem: () => {},
                removeItem: () => {},
                clear: () => {}
            };
        }

        // Initialize real game managers for accurate economics
        this.tokenEconomyManager = new TokenEconomyManager();

        // Connect wallet to initialize dailyRewardTracker
        this.tokenEconomyManager.walletAddress = this.wallet.address;
        this.tokenEconomyManager.walletConnected = true;

        // Initialize nonce tracking for this user wallet
        this.lastNonce = null;
        this.nonceLock = false;
        this.tokenEconomyManager.initializeDailyRewardTracker();

        // Reset daily reward tracker to ensure clean state for testing
        if (this.tokenEconomyManager.dailyRewardTracker) {
            this.tokenEconomyManager.dailyRewardTracker.reset();
        }

        this.ethTestModeManager = new ETHTestModeManager(this.tokenEconomyManager);

        // ENABLE ETH TEST MODE for stress testing
        this.ethTestModeManager.isTestMode = true;
        console.log(`🧪 ${this.id}: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)`);

        // Enhanced rate limiting and connection management with circuit breaker
        this.requestQueue = [];
        this.isProcessingQueue = false;
        this.maxConcurrentRequests = 1; // Limit to 1 concurrent API call per user to prevent overload
        this.requestDelay = 750; // Increased minimum delay between requests to 750ms
        this.lastRequestTime = 0;
        this.retryAttempts = 7; // Increased retry attempts for better resilience
        this.retryDelay = 1500; // Base retry delay

        // Enhanced circuit breaker pattern for API endpoints
        this.circuitBreakerManager = new CircuitBreakerManager();
        this.initializeCircuitBreakers();

        // Fallback system for graceful degradation
        this.fallbackSystem = new FallbackSystem(this.config);

        // Enhanced connection management
        this.activeConnections = 0;
        this.maxActiveConnections = 2;
        this.connectionTimeout = 15000; // 15 second timeout

        // Behavior configuration based on user type
        this.behaviorConfig = this.getBehaviorConfig(type);

        // State tracking
        this.sessionData = {
            transactions: [],
            rewards: [],
            purchases: [],
            environments: [],
            startTime: null,
            endTime: null
        };

        this.safeLog(`👤 Created ${type} user simulator: ${id} (${account.address})`);
    }

    /**
     * Safe logging that handles EPIPE errors
     */
    safeLog(message) {
        try {
            console.log(message);
        } catch (error) {
            if (error.code === 'EPIPE') {
                // Silently ignore EPIPE errors to prevent crashes
                return;
            }
            // Re-throw other errors
            throw error;
        }
    }

    /**
     * Get behavior configuration for user type
     */
    getBehaviorConfig(type) {
        const configs = {
            grinder: {
                playPattern: 'max_completion',
                spendingBehavior: 'zero_spending',
                rewardEarning: 'maximum',
                sessionDuration: 180000, // 3 minutes
                levelsPerSession: 10,
                completionRate: 1.0, // 100% completion
                purchaseProbability: 0.0, // Never purchases
                maxRewardMultiplier: 1.25 * 1.125 * 1.075 // All bonuses
            },
            whale: {
                playPattern: 'minimal_grinding',
                spendingBehavior: 'heavy_purchasing',
                rewardEarning: 'minimal',
                sessionDuration: 60000, // 1 minute
                levelsPerSession: 2,
                completionRate: 0.3, // 30% completion
                purchaseProbability: 0.8, // 80% purchase rate
                maxRewardMultiplier: 1.0 // No bonuses
            },
            creator: {
                playPattern: 'content_creation',
                spendingBehavior: 'environment_purchases',
                rewardEarning: 'creator_rewards',
                sessionDuration: 120000, // 2 minutes
                levelsPerSession: 5,
                completionRate: 0.6, // 60% completion
                purchaseProbability: 0.3, // 30% purchase rate
                environmentCreationRate: 0.7, // 70% chance to create environment
                creatorRewardRate: 0.5 // 50% of environment sales
            },
            casual: {
                playPattern: 'moderate_play',
                spendingBehavior: 'occasional_powerups',
                rewardEarning: 'moderate',
                sessionDuration: 150000, // 2.5 minutes
                levelsPerSession: 7,
                completionRate: 0.6, // 60% completion
                purchaseProbability: 0.2, // 20% purchase rate
                maxRewardMultiplier: 1.125 // Some bonuses
            }
        };

        return configs[type] || configs.casual;
    }

    /**
     * Initialize circuit breakers for different API endpoints
     */
    initializeCircuitBreakers() {
        const endpoints = [
            { name: 'wallet-send', failureThreshold: 5, timeout: 30000 },
            { name: 'environment-purchase', failureThreshold: 3, timeout: 45000 },
            { name: 'generate-environment', failureThreshold: 2, timeout: 60000 },
            { name: 'tokens-award', failureThreshold: 5, timeout: 30000 },
            { name: 'tokens-spend', failureThreshold: 5, timeout: 30000 }
        ];

        endpoints.forEach(({ name, failureThreshold, timeout }) => {
            this.circuitBreakerManager.getBreaker(name, {
                failureThreshold,
                timeout,
                successThreshold: 2,
                monitoringPeriod: 60000
            });
        });
    }

    /**
     * Get circuit breaker name for endpoint
     */
    getCircuitBreakerName(endpoint) {
        if (endpoint.includes('/wallet/send')) return 'wallet-send';
        if (endpoint.includes('/environments/') && endpoint.includes('/purchase')) return 'environment-purchase';
        if (endpoint.includes('/generate-environment')) return 'generate-environment';
        if (endpoint.includes('/tokens/award')) return 'tokens-award';
        if (endpoint.includes('/tokens/spend')) return 'tokens-spend';
        return 'wallet-send'; // Default fallback
    }

    /**
     * Simulate a complete gaming session
     */
    async simulateGamingSession() {
        try {
            this.safeLog(`🎮 Starting ${this.type} session for ${this.id}...`);
            this.sessionData.startTime = Date.now();

            // Authenticate with the game server
            await this.authenticateWithServer();

            // Execute behavior pattern based on user type
            switch (this.type) {
                case 'grinder':
                    await this.simulateGrindingSession();
                    break;
                case 'whale':
                    await this.simulateWhaleSession();
                    break;
                case 'creator':
                    await this.simulateCreatorSession();
                    break;
                case 'casual':
                    await this.simulateCasualSession();
                    break;
            }

            this.sessionData.endTime = Date.now();
            this.safeLog(`✅ Completed ${this.type} session for ${this.id}`);

        } catch (error) {
            console.error(`❌ Error in ${this.type} session for ${this.id}:`, error);
            throw error;
        }
    }

    /**
     * Authenticate with the game server (simulated)
     */
    async authenticateWithServer() {
        // In a real implementation, this would handle OrangeID authentication
        // For testing, we'll simulate successful authentication
        this.safeLog(`🔐 Authenticated ${this.id} with server`);
    }

    /**
     * Simulate grinder behavior - max rewards, zero spending using REAL game economics
     */
    async simulateGrindingSession() {
        // Reset daily reward tracker to ensure clean state for this grinding session
        if (this.tokenEconomyManager.dailyRewardTracker) {
            this.tokenEconomyManager.dailyRewardTracker.reset();
        }

        // Add staggered delays for grinder profiles to prevent concurrent overload
        const grinderDelay = this.id.includes('grinder') ?
            (parseInt(this.id.split('_')[1]) || 1) * 1500 : 0; // 1.5s, 3s, 4.5s delays

        if (grinderDelay > 0) {
            this.safeLog(`⏳ Grinder ${this.id} waiting ${grinderDelay}ms to prevent server overload...`);
            await this.delay(grinderDelay);
        }

        const { levelsPerSession } = this.behaviorConfig;

        for (let level = 1; level <= levelsPerSession; level++) {
            // Simulate level completion by calling the GAME's level completion system
            const completionData = {
                levelNumber: level,
                completed: true,
                completionTime: 30 + Math.random() * 20, // 30-50 seconds
                score: { totalScore: 1000 + Math.random() * 500 }, // Random score
                enemiesDefeated: 50, // Perfect completion
                totalEnemies: 50,
                perfectCompletion: true,
                bonuses: { speed: true, accuracy: true, perfect: true },
                nextLevel: level + 1
            };

            this.safeLog(`🎮 Level ${level}: Simulating completion...`);

            try {
                // Call the GAME's level completion handler - this will handle daily limits internally
                const result = await this.tokenEconomyManager.handleLevelCompletion(
                    completionData,
                    completionData.score.totalScore,
                    { totalEnemies: 50 } // levelConfig
                );

                if (result.success && result.tokensAwarded > 0) {
                    this.safeLog(`🎁 Game awarded ${result.tokensAwarded} WISH tokens`);
                } else {
                    this.safeLog(`🚫 Level ${level}: No reward (${result.reason || 'game decision'})`);
                }
            } catch (error) {
                this.safeLog(`❌ Level ${level} completion failed: ${error.message}`);
            }

            // Longer delay between levels to prevent server overload
            await this.delay(1000 + Math.random() * 500); // 1-1.5s delay
        }

        this.safeLog(`🏆 Grinder ${this.id} completed ${levelsPerSession} levels with REAL rewards`);
    }

    /**
     * Simulate whale behavior - heavy spending, minimal grinding
     */
    async simulateWhaleSession() {
        const { levelsPerSession, purchaseProbability } = this.behaviorConfig;

        // Heavy purchasing before playing
        await this.simulateHeavyPurchasing();

        // Minimal grinding with purchased power-ups - USE GAME'S LEVEL COMPLETION SYSTEM
        for (let level = 1; level <= levelsPerSession; level++) {
            // Simulate minimal effort level completion (30% enemies defeated)
            const completionData = {
                levelNumber: level,
                completed: true,
                completionTime: 60 + Math.random() * 30, // Slower completion (60-90s)
                score: { totalScore: 300 + Math.random() * 200 }, // Lower score
                enemiesDefeated: 15, // 30% completion for minimal effort
                totalEnemies: 50,
                perfectCompletion: false,
                bonuses: { speed: false, accuracy: false, perfect: false },
                nextLevel: level + 1
            };

            this.safeLog(`🎮 Level ${level}: Simulating minimal effort completion...`);

            try {
                // Call the GAME's level completion handler
                const result = await this.tokenEconomyManager.handleLevelCompletion(
                    completionData,
                    completionData.score.totalScore,
                    { totalEnemies: 50 }
                );

                if (result.success && result.tokensAwarded > 0) {
                    this.safeLog(`🎁 Game awarded ${result.tokensAwarded} WISH tokens`);
                } else {
                    this.safeLog(`🚫 Level ${level}: No reward (${result.reason || 'game decision'})`);
                }
            } catch (error) {
                this.safeLog(`❌ Level ${level} completion failed: ${error.message}`);
            }

            await this.delay(1000);
        }

        this.safeLog(`💰 Whale ${this.id} completed session with heavy spending`);
    }

    /**
     * Simulate creator behavior - environment creation and moderate play
     */
    async simulateCreatorSession() {
        // Create custom environments
        await this.simulateEnvironmentCreation();

        // Moderate gameplay
        await this.simulateModerateGameplay();

        this.safeLog(`🎨 Creator ${this.id} completed session with environment creation`);
    }

    /**
     * Simulate casual player behavior
     */
    async simulateCasualSession() {
        const { levelsPerSession, purchaseProbability } = this.behaviorConfig;

        // Occasional purchases
        if (Math.random() < purchaseProbability) {
            await this.simulateOccasionalPurchase();
        }

        // Moderate gameplay
        await this.simulateModerateGameplay();

        this.safeLog(`🎯 Casual player ${this.id} completed session`);
    }

    /**
     * Simulate environment creation (creator behavior)
     */
    async simulateEnvironmentCreation() {
        const startTime = Date.now();
        try {
            const environmentDescription = this.generateRandomEnvironmentDescription();

            // ETH Test Mode: Reality Warp costs 2500 ETH (10% of 25,000 WISH)
            const ethCost = GAME_CONFIG.POWER_UP_COSTS.REALITY_WARP * 0.1; // 2,500 ETH in ETH test mode

            this.safeLog(`🎨 ${this.id} creating environment: "${environmentDescription}"`);
            this.safeLog(`💸 ${this.id} spending ${ethCost} ETH for: Reality Warp: ${environmentDescription}`);

            // First, make the REAL blockchain transaction for environment creation
            await this.sendETHToHotWallet(ethCost, `Reality Warp: ${environmentDescription}`);

            const requestData = {
                environmentDescription,
                creatorUserId: this.id,
                creatorAddress: this.wallet.address,
                purchaseAmount: ethCost
            };

            // MOCK the environment creation - no real API calls to save money
            const duration = Date.now() - startTime;

            // Small delay to simulate API processing
            await new Promise(resolve => setTimeout(resolve, 200));

            // Create mock environment response
            const environment = {
                id: `env_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                name: `${environmentDescription.split(' ').slice(0, 3).join(' ')} Environment`,
                description: environmentDescription,
                creatorAddress: this.wallet.address,
                creatorUserId: this.wallet.address, // Use wallet address as creator ID for proper reward distribution
                imageUrl: `https://mock-image-url.com/${Math.random().toString(36).substr(2, 9)}.jpg`,
                createdAt: new Date().toISOString(),
                purchaseAmount: ethCost,
                difficulty: 'medium',
                tags: ['mock', 'test', 'environment']
            };
            this.sessionData.environments.push(environment);

            // Track successful API call and environment creation
            if (this.transactionTracker) {
                this.transactionTracker.trackAPICall('/generate-environment', 'POST', requestData, environment, duration);
                this.transactionTracker.trackEnvironmentCreation(this.id, environment, ethCost, 0);
            }

            this.safeLog(`✅ Environment created by ${this.id}: ${environment.name}`);
            this.safeLog(`✅ Environment created: ${environment.name} by ${this.id}`);
            return environment;

        } catch (error) {
            console.error(`❌ Environment creation failed for ${this.id}:`, error);
            throw error;
        }
    }

 
    /**
     * Execute mystical environment purchase for a specific environment
     */
    async executeMysticalEnvironmentPurchase(environmentId, ethCost) {
        const response = await this.rateLimitedFetch(`${this.config.apiBaseUrl}/environments/${environmentId}/purchase`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.authToken}`,
                'X-Request-ID': `${this.id}-${Date.now()}`
            },
            body: JSON.stringify({
                purchaserUserId: this.id,
                purchaserAddress: this.wallet.address,
                cost: ethCost
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Mystical environment purchase API failed: ${response.status} - ${errorText}`);
        }

        const result = await response.json();
        this.safeLog(`✅ REAL Mystical Environment purchased: ${result.environmentName || 'Test Environment'} for ${ethCost} ETH`);

        if (result.creatorReward) {
            this.safeLog(`🎁 Creator reward distributed: ${result.creatorReward.amount} ETH to ${result.creatorReward.recipient}`);
        }

        return result;
    }

    /**
     * Simulate heavy purchasing (whale behavior) using REAL game prices
     */
    async simulateHeavyPurchasing() {
        // Use REAL power-up costs from GAME_CONFIG (Reality Warp is environment creation, not a power-up)
        const realPurchases = [
            { wishCost: GAME_CONFIG.POWER_UP_COSTS.EXTRA_LIFE, item: 'Extra Life' },
            { wishCost: GAME_CONFIG.POWER_UP_COSTS.EXTRA_WINGMAN, item: 'Extra Wingman' },
            { wishCost: GAME_CONFIG.POWER_UP_COSTS.SPREAD_AMMO, item: 'Spread Ammo' }
        ];

        for (let i = 0; i < realPurchases.length; i++) {
            const purchase = realPurchases[i];
            
            // ETH Test Mode: Use 10% of WISH cost as ETH cost
            const ethCost = purchase.wishCost * 0.1;

            this.safeLog(`🛒 ${purchase.item}: ${ethCost} ETH (ETH test mode)`);

            await this.sendETHToHotWallet(ethCost, purchase.item);
            
            // Add progressive delays between transactions to prevent nonce collisions
            // First transaction: 300ms, Second: 500ms, Third: 700ms
            const delay = 300 + (i * 200) + Math.random() * 100;
            this.safeLog(`⏳ Waiting ${delay}ms before next transaction...`);
            await this.delay(delay);
        }
    }

    /**
     * Simulate occasional purchase (casual behavior) using REAL game prices
     */
    async simulateOccasionalPurchase() {
        // Use REAL power-up costs from GAME_CONFIG (casual players buy cheaper items)
        const casualPurchases = [
            { wishCost: GAME_CONFIG.POWER_UP_COSTS.SPREAD_AMMO, item: 'Spread Ammo' },
            { wishCost: GAME_CONFIG.POWER_UP_COSTS.EXTRA_WINGMAN, item: 'Extra Wingman' },
            { wishCost: GAME_CONFIG.POWER_UP_COSTS.EXTRA_LIFE, item: 'Extra Life' }
        ];

        const randomPurchase = casualPurchases[Math.floor(Math.random() * casualPurchases.length)];

        // ETH Test Mode: Use 10% of WISH cost as ETH cost
        const ethCost = randomPurchase.wishCost * 0.1;

        this.safeLog(`🛒 Casual ${randomPurchase.item}: ${ethCost} ETH (ETH test mode)`);

        await this.sendETHToHotWallet(ethCost, randomPurchase.item);
    }

    /**
     * Simulate moderate gameplay - USE GAME'S LEVEL COMPLETION SYSTEM
     */
    async simulateModerateGameplay() {
        const { levelsPerSession, completionRate } = this.behaviorConfig;

        for (let level = 1; level <= levelsPerSession; level++) {
            // Simulate moderate level completion based on completionRate
            const enemiesDefeated = Math.floor(50 * completionRate);
            const completionData = {
                levelNumber: level,
                completed: true,
                completionTime: 40 + Math.random() * 20, // Moderate completion time (40-60s)
                score: { totalScore: 600 + Math.random() * 300 }, // Moderate score
                enemiesDefeated: enemiesDefeated,
                totalEnemies: 50,
                perfectCompletion: enemiesDefeated === 50,
                bonuses: {
                    speed: completionRate > 0.8,
                    accuracy: completionRate > 0.7,
                    perfect: enemiesDefeated === 50
                },
                nextLevel: level + 1
            };

            this.safeLog(`🎮 Level ${level}: Simulating moderate completion (${Math.floor(completionRate * 100)}%)...`);

            try {
                // Call the GAME's level completion handler
                const result = await this.tokenEconomyManager.handleLevelCompletion(
                    completionData,
                    completionData.score.totalScore,
                    { totalEnemies: 50 }
                );

                if (result.success && result.tokensAwarded > 0) {
                    this.safeLog(`🎁 Game awarded ${result.tokensAwarded} WISH tokens`);
                } else {
                    this.safeLog(`🚫 Level ${level}: No reward (${result.reason || 'game decision'})`);
                }
            } catch (error) {
                this.safeLog(`❌ Level ${level} completion failed: ${error.message}`);
            }

            await this.delay(800);
        }
    }

    /**
     * Send ETH reward to user - REAL ETH transfer from hot wallet
     * This is called AFTER the game has decided to award tokens
     */
    async sendETHReward(ethAmount, reason) {
        const startTime = Date.now();
        try {
            this.safeLog(`💰 Sending ${ethAmount} ETH to ${this.id} for: ${reason}`);

            // Call REAL game API to send ETH from hot wallet to user
            const requestData = {
                toAddress: this.wallet.address,
                amount: ethAmount.toString(),
                reason: `ETH reward: ${reason}`
            };

            const response = await this.rateLimitedFetch(`${this.config.apiBaseUrl}/wallet/send`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer secure-token-for-development`
                },
                body: JSON.stringify(requestData)
            });

            const duration = Date.now() - startTime;

            if (!response.ok) {
                const errorText = await response.text();
                const error = new Error(`ETH reward failed: ${response.status} - ${errorText}`);
                // Track failed API call
                if (this.transactionTracker) {
                    this.transactionTracker.trackAPICall('/wallet/send', 'POST', requestData, null, duration, error);
                }
                throw error;
            }

            const result = await response.json();
            this.sessionData.rewards.push(result);

            // Track this REAL blockchain transaction
            if (this.transactionTracker) {
                this.transactionTracker.trackAPICall('/wallet/send', 'POST', requestData, result, duration);
            }

            this.safeLog(`✅ REAL ETH transfer completed: ${ethAmount} ETH to ${this.wallet.address}`);
            this.safeLog(`📤 Transaction hash: ${result.transactionHash}`);

            return result.transactionHash;

        } catch (error) {
            console.error(`❌ ETH reward failed for ${this.id}:`, error);
            throw error;
        }
    }

    /**
     * Send ETH from user wallet to hot wallet - REAL BLOCKCHAIN TRANSACTION
     */
    async sendETHToHotWallet(ethAmount, reason) {
        try {
            this.safeLog(`💸 ${this.id} sending ${ethAmount} ETH to hot wallet for: ${reason}`);

            // Make REAL blockchain transaction from user wallet to hot wallet
            const amountWei = ethers.parseEther(ethAmount.toString());

            // Get next nonce for this wallet with proper synchronization
            const nonce = await this.getNextUserNonce();

            // Create transaction object with proper gas settings
            const transaction = {
                to: this.config.hotWalletAddress,
                value: amountWei,
                nonce: nonce,
                gasLimit: 21000, // Standard ETH transfer gas limit
                gasPrice: ethers.parseUnits('20', 'gwei'), // 20 gwei gas price
                chainId: this.config.chainId // Explicit chain ID
            };

            // Sign and send transaction using the wallet
            const signedTx = await this.wallet.sendTransaction(transaction);

            // Wait for transaction to be mined
            const receipt = await signedTx.wait();

            this.safeLog(`✅ REAL blockchain transaction mined: ${receipt.hash}`);
            this.safeLog(`⛽ Gas used: ${receipt.gasUsed.toString()}`);
            this.safeLog(`💸 ${this.id} completed REAL ETH transfer: ${ethAmount} ETH to hot wallet`);

            return receipt.hash;

        } catch (error) {
            this.safeLog(`❌ ETH transfer failed for ${this.id}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Spend tokens from user - REAL BLOCKCHAIN TRANSACTIONS
     */
    async spendTokens(wishAmount, reason) {
        const startTime = Date.now();
        try {
            // Convert WISH to ETH for blockchain transaction (1 WISH = 0.001 ETH in test mode)
            const ethAmount = wishAmount * 0.001;

            this.safeLog(`💸 ${this.id} making REAL blockchain transaction: ${wishAmount} WISH → ${ethAmount} ETH for: ${reason}`);
            this.safeLog(`🔗 Sending ${ethAmount} ETH from ${this.account.address} to hot wallet ${this.config.hotWalletAddress}`);

            // Make REAL blockchain transaction from user wallet to hot wallet
            const txHash = await this.sendETHToHotWallet(ethAmount, reason);

            if (!txHash) {
                throw new Error('Failed to send ETH to hot wallet - no transaction hash returned');
            }

            // Create transaction record
            const transactionResult = {
                success: true,
                transactionId: `user_purchase_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                blockchainTxHash: txHash,
                fromAddress: this.wallet.address,
                toAddress: this.config.hotWalletAddress,
                ethAmount: ethAmount,
                wishAmount: wishAmount,
                reason: reason,
                timestamp: Date.now(),
                realBlockchainTransaction: true
            };

            this.sessionData.purchases.push(transactionResult);

            // Track the REAL blockchain transaction
            if (this.transactionTracker) {
                this.transactionTracker.trackETHTransfer(
                    this.wallet.address,
                    this.config.hotWalletAddress,
                    ethAmount,
                    `Purchase: ${reason}`,
                    txHash,
                    {
                        purchaseType: 'user_to_hot_wallet',
                        userType: this.type,
                        wishAmount: wishAmount,
                        ethAmount: ethAmount,
                        realBlockchainTransaction: true
                    }
                );
            }

            this.safeLog(`✅ REAL blockchain transaction completed: ${ethAmount} ETH sent to hot wallet`);
            this.safeLog(`📤 Blockchain transaction hash: ${txHash}`);
            this.safeLog(`💸 ${this.id} completed REAL purchase transaction for: ${reason}`);

            return transactionResult;

        } catch (error) {
            console.error(`❌ REAL blockchain transaction failed for ${this.id}:`, error);
            throw error;
        }
    }

    /**
     * Enhanced rate-limited fetch with circuit breaker, retry logic and connection management
     */
    async rateLimitedFetch(url, options = {}) {
        // Extract endpoint for circuit breaker
        const endpoint = this.extractEndpoint(url);
        const breakerName = this.getCircuitBreakerName(endpoint);

        // Acquire global slot if global rate limiter is available
        if (this.globalRateLimiter) {
            await this.globalRateLimiter.acquireGlobalSlot();
        }

        try {
            // Execute through circuit breaker
            return await this.circuitBreakerManager.execute(breakerName, async () => {
                // CRITICAL: Add stress test header to bypass file saving on server
                const stressTestOptions = {
                    ...options,
                    headers: {
                        ...options?.headers,
                        'X-Stress-Test': 'true'
                    }
                };
                return this.executeRequest(url, stressTestOptions);
            });
        } finally {
            // Release global slot in finally block to ensure it's always released
            if (this.globalRateLimiter) {
                this.globalRateLimiter.releaseGlobalSlot();
            }
        }
    }

    /**
     * Execute the actual HTTP request with enhanced retry logic
     */
    async executeRequest(url, options = {}) {

        // Check connection limits
        if (this.activeConnections >= this.maxActiveConnections) {
            await this.waitForConnectionSlot();
        }

        // Ensure minimum delay between requests
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        if (timeSinceLastRequest < this.requestDelay) {
            await this.delay(this.requestDelay - timeSinceLastRequest);
        }
        this.lastRequestTime = Date.now();

        this.activeConnections++;

        try {
            // Enhanced retry logic with exponential backoff and jitter
            for (let attempt = 0; attempt < this.retryAttempts; attempt++) {
                try {
                    // Enhanced timeout options with connection management
                    const timeoutOptions = {
                        ...options,
                        timeout: this.connectionTimeout,
                        headers: {
                            'Connection': 'close', // Prevent connection reuse issues
                            'User-Agent': `StressTest-${this.id}`,
                            'X-Request-ID': `${this.id}-${Date.now()}-${attempt}`,
                            ...options.headers
                        }
                    };

                    const response = await fetch(url, timeoutOptions);

                    // Handle different response types
                    if (response.ok) {
                        return response;
                    }

                    // Handle rate limiting (429) with exponential backoff
                    if (response.status === 429) {
                        const retryAfter = response.headers.get('retry-after');
                        const retryDelay = retryAfter ? parseInt(retryAfter) * 1000 : this.calculateBackoffDelay(attempt, true);

                        if (attempt < this.retryAttempts - 1) {
                            this.safeLog(`⚠️ ${this.id}: Rate limited (429) for ${url}, retrying in ${retryDelay}ms`);
                            await this.delay(retryDelay);
                            continue;
                        }
                        throw new Error(`Rate limited: ${response.status}`);
                    }

                    // Handle server errors (5xx) with retry
                    if (response.status >= 500) {
                        if (attempt < this.retryAttempts - 1) {
                            const backoffDelay = this.calculateBackoffDelay(attempt);
                            this.safeLog(`⚠️ ${this.id}: Server error ${response.status} for ${url}, retrying in ${backoffDelay}ms`);
                            await this.delay(backoffDelay);
                            continue;
                        }
                        throw new Error(`Server error: ${response.status}`);
                    }

                    // For client errors (4xx), don't retry
                    if (response.status >= 400) {
                        throw new Error(`Client error: ${response.status}`);
                    }

                    return response;

                } catch (error) {
                    const isLastAttempt = attempt === this.retryAttempts - 1;

                    if (isLastAttempt) {
                        this.safeLog(`❌ ${this.id}: Final attempt failed for ${url}: ${error.message}`);
                        throw error;
                    }

                    // Calculate backoff delay with jitter
                    const backoffDelay = this.calculateBackoffDelay(attempt);
                    this.safeLog(`⚠️ ${this.id}: Attempt ${attempt + 1} failed for ${url}, retrying in ${backoffDelay}ms`);
                    await this.delay(backoffDelay);
                }
            }
        } finally {
            this.activeConnections--;
        }
    }

    /**
     * Calculate exponential backoff delay with jitter
     */
    calculateBackoffDelay(attempt, isRateLimit = false) {
        const baseDelay = isRateLimit ? 3000 : this.retryDelay; // Higher base for rate limits
        const exponentialDelay = baseDelay * Math.pow(2, attempt);
        const jitter = Math.random() * 1000; // Add up to 1 second of jitter
        const maxDelay = isRateLimit ? 30000 : 15000; // Cap delays

        return Math.min(exponentialDelay + jitter, maxDelay);
    }

    /**
     * Extract endpoint pattern from URL for circuit breaker
     */
    extractEndpoint(url) {
        if (url.includes('/wallet/send')) return '/wallet/send';
        if (url.includes('/environments/') && url.includes('/purchase')) return '/environments/:id/purchase';
        if (url.includes('/generate-environment')) return '/generate-environment';
        if (url.includes('/tokens/award')) return '/tokens/award';
        if (url.includes('/tokens/spend')) return '/tokens/spend';
        return '/wallet/send'; // Default fallback
    }

    /**
     * Wait for available connection slot
     */
    async waitForConnectionSlot() {
        const maxWaitTime = 15000; // 15 seconds max wait (increased from 5s)
        const startTime = Date.now();

        while (this.activeConnections >= this.maxActiveConnections) {
            if (Date.now() - startTime > maxWaitTime) {
                throw new Error('Timeout waiting for connection slot');
            }
            await this.delay(100); // Check every 100ms
        }
    }

    /**
     * Generate random environment description for creators
     */
    generateRandomEnvironmentDescription() {
        const themes = [
            'Mystical crystal caverns with floating islands',
            'Cyberpunk neon cityscape with rain',
            'Ancient temple ruins in a jungle',
            'Underwater coral reef with bioluminescence',
            'Volcanic landscape with lava flows',
            'Ice planet with aurora borealis',
            'Desert oasis with sandstorms',
            'Space station with nebula backdrop'
        ];

        return themes[Math.floor(Math.random() * themes.length)];
    }

    /**
     * Simulate coordinated behavior across multiple accounts
     */
    async simulateCoordinatedBehavior() {
        this.safeLog(`🤝 ${this.id} starting coordinated behavior simulation`);

        // Simulate coordinated actions that might stress the system
        await this.simulateGamingSession();

        // Add small random delay to avoid perfect synchronization
        await this.delay(Math.random() * 2000);
    }

    /**
     * Simulate maximum stress behavior
     */
    async simulateMaxStressBehavior() {
        this.safeLog(`⚡ ${this.id} starting maximum stress behavior`);
        
        // Rapid-fire actions to stress test the system
        const promises = [];
        
        // Multiple concurrent actions
        if (this.type === 'grinder') {
            promises.push(this.simulateGrindingSession());
        } else if (this.type === 'whale') {
            promises.push(this.simulateHeavyPurchasing());
        } else if (this.type === 'creator') {
            promises.push(this.simulateEnvironmentCreation());
        }
        
        await Promise.all(promises);
    }

    /**
     * Get session summary
     */
    getSessionSummary() {
        return {
            userId: this.id,
            userType: this.type,
            account: this.account.address,
            sessionDuration: this.sessionData.endTime - this.sessionData.startTime,
            totalRewards: this.sessionData.rewards.reduce((sum, r) => sum + r.amount, 0),
            totalSpent: this.sessionData.purchases.reduce((sum, p) => sum + p.amount, 0),
            transactionCount: this.sessionData.transactions.length,
            environmentsCreated: this.sessionData.environments.length
        };
    }

    /**
     * Get next nonce for user wallet with proper synchronization
     * @returns {Promise<number>} Next available nonce
     */
    async getNextUserNonce() {
        // Wait for any existing nonce operation to complete
        while (this.nonceLock) {
            await this.delay(10);
        }
        
        this.nonceLock = true;
        
        try {
            // Get current nonce from network
            let networkNonce = await this.provider.getTransactionCount(this.wallet.address, 'pending');
            
            // If we have a last nonce tracked, ensure we use the next one
            if (this.lastNonce !== null && networkNonce <= this.lastNonce) {
                networkNonce = this.lastNonce + 1;
            }
            
            // Update our tracking
            this.lastNonce = networkNonce;
            
            this.safeLog(`🔢 Next nonce for ${this.id}: ${networkNonce}`);
            return networkNonce;
        } finally {
            this.nonceLock = false;
        }
    }

    /**
     * Utility method for delays
     */
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
