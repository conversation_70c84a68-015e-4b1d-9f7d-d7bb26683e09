npm run test:tokenomics

> WarpSector-game@1.0.0 test:tokenomics
> node test/tokenomics/runStressTest.js

🚀 Starting WarpSector Tokenomics Stress Test
============================================================
📋 Test Configuration:
   Duration: 300 seconds
   Max Users: 10
   API URL: http://localhost:3001/api
   Hardhat URL: http://localhost:8545
   Hot Wallet: ******************************************
   Scenario: All scenarios
   Verbose: Disabled

🔍 Validating prerequisites...
   ✅ Game Server: OK
   ✅ Hardhat Node: OK
   ✅ Hot Wallet Balance: OK
   ✅ Test Accounts: OK
✅ All prerequisites validated

🧪 TokenomicsStressTest initialized
📊 Configuration: {
  apiBaseUrl: 'http://localhost:3001/api',
  hardhatUrl: 'http://localhost:8545',
  chainId: 31337,
  hotWalletAddress: '******************************************',
  testDuration: 300000,
  maxConcurrentUsers: 10,
  verbose: false,
  reportFile: null,
  scenario: null
}
🚀 Initializing Tokenomics Stress Test Framework...
✅ Server health check passed: { status: 'OK', message: 'AI Service Server is running' }
🔗 Connected to network: Network {}
💰 Hot wallet balance: 10000.0 ETH
👥 Found 20 test accounts
📊 TransactionTracker initialized
💰 TreasuryMonitor initialized
📊 Monitoring systems initialized
✅ Initialized daily reward tracker for user: ******************************************
🧪 grinder_1: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
👤 Created grinder user simulator: grinder_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 grinder_2: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
👤 Created grinder user simulator: grinder_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 grinder_3: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
👤 Created grinder user simulator: grinder_3 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 whale_1: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
👤 Created whale user simulator: whale_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 whale_2: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
👤 Created whale user simulator: whale_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 creator_1: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
👤 Created creator user simulator: creator_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 creator_2: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
👤 Created creator user simulator: creator_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 casual_1: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
👤 Created casual user simulator: casual_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 casual_2: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
👤 Created casual user simulator: casual_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 casual_3: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
👤 Created casual user simulator: casual_3 (******************************************)
👥 Created 10 user simulators
✅ Stress test framework initialized successfully
🚀 Starting Tokenomics Stress Test...
💰 Initial treasury balance: 10000 ETH
🚀 TreasuryMonitor started
🚀 TransactionTracker started
📊 Monitoring started
🔍 Queue health monitoring started
🎮 Running Sequential Grinding Test (Attack Vector 1)...
📋 Objective: Test if grinder earnings can drain treasury
👥 Testing with 3 grinder accounts
🎨 Running Creator Reward Test (Attack Vector 2)...
📋 Objective: Test creator reward sustainability and 50% distribution accuracy
👨‍🎨 Testing with 2 creators and 2 whales
🎨 Phase 1: Environment Creation
🎨 creator_1 creating environment: "Mystical crystal caverns with floating islands"
💸 creator_1 spending 2500 ETH for: Reality Warp: Mystical crystal caverns with floating islands
💸 creator_1 sending 2500 ETH to hot wallet for: Reality Warp: Mystical crystal caverns with floating islands
👥 Running Multi-Account Coordination Test (Attack Vector 3)...
📋 Objective: Test economic balance under coordinated behavior
🤝 Simulating coordinated behavior across 5 accounts
⚡ Starting coordinated actions...
💰 Running Treasury Drain Test (Maximum Stress)...
📋 Objective: Test maximum stress on treasury with all users acting simultaneously
💰 Initial treasury balance: 10000 ETH
🎯 Starting grinder session: grinder_1
⏳ Grinder grinder_1 waiting 1500ms to prevent server overload...
💰 Pre-stress treasury balance: 10000 ETH
⚡ Initiating maximum stress scenario...
🔢 Next nonce for creator_1: 0
🤝 grinder_1 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_1...
🔐 Authenticated grinder_1 with server
⏳ Grinder grinder_1 waiting 1500ms to prevent server overload...
⚡ grinder_1 starting maximum stress behavior
⏳ Grinder grinder_1 waiting 1500ms to prevent server overload...
✅ REAL blockchain transaction mined: 0xbc052ae0148bb27663cf3fde061a428600c8a2ef08cf2f3cdf9dfe321f7ffecf
⛽ Gas used: 21000
💸 creator_1 completed REAL ETH transfer: 2500 ETH to hot wallet
⚡ grinder_2 starting maximum stress behavior
⏳ Grinder grinder_2 waiting 3000ms to prevent server overload...
🤝 grinder_2 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_2...
🔐 Authenticated grinder_2 with server
⏳ Grinder grinder_2 waiting 3000ms to prevent server overload...
⚡ grinder_3 starting maximum stress behavior
⏳ Grinder grinder_3 waiting 4500ms to prevent server overload...
✅ API Call: POST /generate-environment (168ms)
🎨 Environment Creation Tracked: Mystical crystal caverns Environment by creator_1
✅ Environment created by creator_1: Mystical crystal caverns Environment
✅ Environment created: Mystical crystal caverns Environment by creator_1
✅ Environment created: Mystical crystal caverns Environment by creator_1
🎨 creator_2 creating environment: "Space station with nebula backdrop"
💸 creator_2 spending 2500 ETH for: Reality Warp: Space station with nebula backdrop
💸 creator_2 sending 2500 ETH to hot wallet for: Reality Warp: Space station with nebula backdrop
🔢 Next nonce for creator_2: 0
⚡ whale_1 starting maximum stress behavior
🛒 Extra Life: 1500 ETH (ETH test mode)
💸 whale_1 sending 1500 ETH to hot wallet for: Extra Life
🔢 Next nonce for whale_1: 0
🤝 grinder_3 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_3...
🔐 Authenticated grinder_3 with server
⏳ Grinder grinder_3 waiting 4500ms to prevent server overload...
✅ REAL blockchain transaction mined: 0x9f9a8649bac0e5dd04fa311852491cb95bc22335c1a034034d2475db6928465c
⛽ Gas used: 21000
💸 creator_2 completed REAL ETH transfer: 2500 ETH to hot wallet
✅ REAL blockchain transaction mined: 0x7a4c66cb8e56f1715969c500dcd28f5d6a4ee09cc60cd8db5bd62caf44322190
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 1500 ETH to hot wallet
⏳ Waiting 362.74121729791113ms before next transaction...
⚡ whale_2 starting maximum stress behavior
🛒 Extra Life: 1500 ETH (ETH test mode)
💸 whale_2 sending 1500 ETH to hot wallet for: Extra Life
🔢 Next nonce for whale_2: 0
✅ REAL blockchain transaction mined: 0xbaadd156d08bc3e6cd7363b245f3c8df2eef005f01004488162ecc2c1d646080
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 1500 ETH to hot wallet
⏳ Waiting 321.4139040453465ms before next transaction...
⚡ creator_1 starting maximum stress behavior
🎨 creator_1 creating environment: "Space station with nebula backdrop"
💸 creator_1 spending 2500 ETH for: Reality Warp: Space station with nebula backdrop
💸 creator_1 sending 2500 ETH to hot wallet for: Reality Warp: Space station with nebula backdrop
✅ API Call: POST /generate-environment (115ms)
🎨 Environment Creation Tracked: Space station with Environment by creator_2
✅ Environment created by creator_2: Space station with Environment
✅ Environment created: Space station with Environment by creator_2
✅ Environment created: Space station with Environment by creator_2
💰 Phase 2: Mystical Environment Purchases
🐋 whale_1 purchasing Mystical Environment for 25000 tokens
🎁 Expected creator reward: 12500 tokens
💰 whale_1 purchasing Mystical Environment for 1000 ETH
🎁 Expected creator reward: 500 ETH
🔢 Next nonce for creator_1: 1
🤝 whale_1 starting coordinated behavior simulation
🎮 Starting whale session for whale_1...
🔐 Authenticated whale_1 with server
🛒 Extra Life: 1500 ETH (ETH test mode)
💸 whale_1 sending 1500 ETH to hot wallet for: Extra Life
🔢 Next nonce for whale_1: 1
⚡ creator_2 starting maximum stress behavior
🎨 creator_2 creating environment: "Cyberpunk neon cityscape with rain"
💸 creator_2 spending 2500 ETH for: Reality Warp: Cyberpunk neon cityscape with rain
💸 creator_2 sending 2500 ETH to hot wallet for: Reality Warp: Cyberpunk neon cityscape with rain
✅ REAL blockchain transaction mined: 0x7806b830c14b82586788c1a6dba9c010f30f011ae6a7e626fcf9939270397fef
⛽ Gas used: 21000
💸 creator_1 completed REAL ETH transfer: 2500 ETH to hot wallet
🔢 Next nonce for creator_2: 1
⚡ casual_1 starting maximum stress behavior
✅ REAL blockchain transaction mined: 0xf743b895ad6d64acf381c008910ac674f991a3acdc44c64872920353134e7071
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 1500 ETH to hot wallet
⏳ Waiting 316.023256859269ms before next transaction...
🛒 Extra Wingman: 1000 ETH (ETH test mode)
💸 whale_1 sending 1000 ETH to hot wallet for: Extra Wingman
✅ REAL blockchain transaction mined: 0xe2e68df4c95a35f2ec83dc98fb0a1078e5b2a4d02967b8397982fe6fff42ea08
⛽ Gas used: 21000
💸 creator_2 completed REAL ETH transfer: 2500 ETH to hot wallet
🔢 Next nonce for whale_1: 2
⚡ casual_2 starting maximum stress behavior
🤝 whale_2 starting coordinated behavior simulation
🎮 Starting whale session for whale_2...
🔐 Authenticated whale_2 with server
🛒 Extra Life: 1500 ETH (ETH test mode)
💸 whale_2 sending 1500 ETH to hot wallet for: Extra Life
🛒 Extra Wingman: 1000 ETH (ETH test mode)
💸 whale_2 sending 1000 ETH to hot wallet for: Extra Wingman
⚡ casual_3 starting maximum stress behavior
🔢 Next nonce for whale_2: 1
✅ REAL Mystical Environment purchased: Test Environment for 1000 ETH
🎁 Creator reward distributed: 500 ETH to ******************************************
✅ REAL blockchain transaction mined: 0x6c7e292cf2a8897565820af72c26a19b015123247ec89ee1c51633bcf4487e41
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 1000 ETH to hot wallet
⏳ Waiting 573.2287003739968ms before next transaction...
✅ API Call: POST /generate-environment (130ms)
🎨 Environment Creation Tracked: Space station with Environment by creator_1
✅ Environment created by creator_1: Space station with Environment
✅ Environment created: Space station with Environment by creator_1
🔢 Next nonce for whale_2: 2
✅ API Call: POST /generate-environment (122ms)
🎨 Environment Creation Tracked: Cyberpunk neon cityscape Environment by creator_2
✅ Environment created by creator_2: Cyberpunk neon cityscape Environment
✅ Environment created: Cyberpunk neon cityscape Environment by creator_2
✅ REAL blockchain transaction mined: 0x8c02f12cae037990a9b1f8400f625df1b39e08650362dbec604f86673b437abe
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 1500 ETH to hot wallet
✅ REAL blockchain transaction mined: 0x80ac08aeca5357f7ea7d0f8d1daa34d1ed1f0b00362bcf7c771c8ca141e41c3d
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 1000 ETH to hot wallet
⏳ Waiting 308.74162614154534ms before next transaction...
⏳ Waiting 584.2857348505768ms before next transaction...
🛒 Extra Wingman: 1000 ETH (ETH test mode)
💸 whale_1 sending 1000 ETH to hot wallet for: Extra Wingman
🔢 Next nonce for whale_1: 3
✅ REAL blockchain transaction mined: 0xb858ff72093f336796193e7a98e88dfaaa2516d16f7888a54d6ceb65ac75d355
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 1000 ETH to hot wallet
⏳ Waiting 523.9070413907496ms before next transaction...
🛒 Extra Wingman: 1000 ETH (ETH test mode)
💸 whale_2 sending 1000 ETH to hot wallet for: Extra Wingman
🔢 Next nonce for whale_2: 3
🐋 whale_2 purchasing Mystical Environment for 25000 tokens
🎁 Expected creator reward: 12500 tokens
💰 whale_2 purchasing Mystical Environment for 1000 ETH
🎁 Expected creator reward: 500 ETH
🎮 Level 1: Simulating completion...
✅ Level 1 completed! PARTIAL: 649 tokens
🎁 Game awarded 649 WISH tokens
✅ REAL blockchain transaction mined: 0x257c6c856e3a8ed3730cf6ebdf139fd840affff69589ee1d527fec15a60eb1dc
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 1000 ETH to hot wallet
⏳ Waiting 584.7382178181106ms before next transaction...
🛒 Spread Ammo: 750 ETH (ETH test mode)
💸 whale_1 sending 750 ETH to hot wallet for: Spread Ammo
🔢 Next nonce for whale_1: 4
🎮 Level 1: Simulating completion...
🚫 Level 1: No reward (level_already_completed_today)
🎮 Level 1: Simulating completion...
🚫 Level 1: No reward (level_already_completed_today)
✅ REAL blockchain transaction mined: 0x2bac24bf52794d09c87cbf7fb8e39dd294cd30463d905c43ab6ba9a24f6e0fd8
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 750 ETH to hot wallet
⏳ Waiting 765.3656170501315ms before next transaction...
🛒 Spread Ammo: 750 ETH (ETH test mode)
💸 whale_2 sending 750 ETH to hot wallet for: Spread Ammo
✅ REAL Mystical Environment purchased: Test Environment for 1000 ETH
🎁 Creator reward distributed: 500 ETH to ******************************************
🔢 Next nonce for whale_2: 4
✅ REAL blockchain transaction mined: 0x84e2c5aeea24dcd00b9e1b6721904cfe8cdd2f0f7a84d47ff8969255e55b6a0a
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 750 ETH to hot wallet
⏳ Waiting 744.044015833678ms before next transaction...
🛒 Spread Ammo: 750 ETH (ETH test mode)
💸 whale_1 sending 750 ETH to hot wallet for: Spread Ammo
🔢 Next nonce for whale_1: 5
✅ REAL blockchain transaction mined: 0x85e44f1783d06a78cc84ea1fb5540d685ff4327bc460d6813eb6a08e0cd6ae2b
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 750 ETH to hot wallet
⏳ Waiting 763.5383898971229ms before next transaction...
🛒 Spread Ammo: 750 ETH (ETH test mode)
💸 whale_2 sending 750 ETH to hot wallet for: Spread Ammo
🔢 Next nonce for whale_2: 5
📊 Creator Reward Test Results:
   Total Purchases: 50000 tokens
   Expected Creator Rewards: 25000 tokens (50%)
   Environments Created: 2
   Creator Reward Accuracy: Testing 50% distribution...
✅ REAL blockchain transaction mined: 0x7dc6b096240e3f7eab240ed5dce916890c7b48af20e7fcfbc00567006fa927e0
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 750 ETH to hot wallet
⏳ Waiting 750.2157369114009ms before next transaction...
🎮 Level 2: Simulating completion...
✅ Level 2 completed! PARTIAL: 465 tokens
🎁 Game awarded 465 WISH tokens
🎮 Level 1: Simulating minimal effort completion...
✅ Level 1 completed! PARTIAL: 742 tokens
🎁 Game awarded 742 WISH tokens
🎮 Level 2: Simulating completion...
🚫 Level 2: No reward (level_already_completed_today)
🎮 Level 2: Simulating completion...
🚫 Level 2: No reward (level_already_completed_today)
🎮 Level 1: Simulating minimal effort completion...
✅ Level 1 completed! PARTIAL: 490 tokens
🎁 Game awarded 490 WISH tokens
🎮 Level 1: Simulating completion...
✅ Level 1 completed! PARTIAL: 870 tokens
🎁 Game awarded 870 WISH tokens
🎮 Level 1: Simulating completion...
🚫 Level 1: No reward (level_already_completed_today)
🎮 Level 2: Simulating minimal effort completion...
✅ Level 2 completed! PARTIAL: 814 tokens
🎁 Game awarded 814 WISH tokens
🎮 Level 3: Simulating completion...
✅ Level 3 completed! PARTIAL: 675 tokens
🎁 Game awarded 675 WISH tokens
🎮 Level 2: Simulating minimal effort completion...
✅ Level 2 completed! PARTIAL: 467 tokens
🎁 Game awarded 467 WISH tokens
🎮 Level 3: Simulating completion...
🚫 Level 3: No reward (level_already_completed_today)
🎮 Level 3: Simulating completion...
🚫 Level 3: No reward (level_already_completed_today)
🎮 Level 2: Simulating completion...
✅ Level 2 completed! PARTIAL: 950 tokens
🎁 Game awarded 950 WISH tokens
💰 Whale whale_1 completed session with heavy spending
✅ Completed whale session for whale_1
🎮 Level 2: Simulating completion...
🚫 Level 2: No reward (level_already_completed_today)
🎮 Level 1: Simulating completion...
✅ Level 1 completed! PARTIAL: 828 tokens
🎁 Game awarded 828 WISH tokens
🎮 Level 4: Simulating completion...
✅ Level 4 completed! PARTIAL: 1132 tokens
🎁 Game awarded 1132 WISH tokens
🎮 Level 1: Simulating completion...
🚫 Level 1: No reward (level_already_completed_today)
💰 Whale whale_2 completed session with heavy spending
✅ Completed whale session for whale_2
💰 Balance change: +21999.999948 ETH (31999.999948 ETH total)
📈 Treasury inflow detected: +21999.999948 ETH
⚠️ ALERT: Extremely large balance change detected: 21999.999948 ETH
🎮 Level 4: Simulating completion...
🚫 Level 4: No reward (level_already_completed_today)
🎮 Level 3: Simulating completion...
✅ Level 3 completed! PARTIAL: 792 tokens
🎁 Game awarded 792 WISH tokens
🎮 Level 4: Simulating completion...
🚫 Level 4: No reward (level_already_completed_today)
🎮 Level 3: Simulating completion...
🚫 Level 3: No reward (level_already_completed_today)
🎮 Level 2: Simulating completion...
✅ Level 2 completed! PARTIAL: 766 tokens
🎁 Game awarded 766 WISH tokens
🎮 Level 2: Simulating completion...
🚫 Level 2: No reward (level_already_completed_today)
🎮 Level 5: Simulating completion...
✅ Level 5 completed! PARTIAL: 1018 tokens
🎁 Game awarded 1018 WISH tokens
🎮 Level 5: Simulating completion...
🚫 Level 5: No reward (level_already_completed_today)
🎮 Level 4: Simulating completion...
✅ Level 4 completed! PARTIAL: 774 tokens
🎁 Game awarded 774 WISH tokens
🎮 Level 5: Simulating completion...
🚫 Level 5: No reward (level_already_completed_today)
🎮 Level 4: Simulating completion...
🚫 Level 4: No reward (level_already_completed_today)
🎮 Level 3: Simulating completion...
✅ Level 3 completed! PARTIAL: 1037 tokens
🎁 Game awarded 1037 WISH tokens
🎮 Level 3: Simulating completion...
🚫 Level 3: No reward (level_already_completed_today)
🎮 Level 6: Simulating completion...
✅ Level 6 completed! PARTIAL: 1228 tokens
🎁 Game awarded 1228 WISH tokens
🎮 Level 6: Simulating completion...
🚫 Level 6: No reward (level_already_completed_today)
🎮 Level 5: Simulating completion...
✅ Level 5 completed! PARTIAL: 1183 tokens
🎁 Game awarded 1183 WISH tokens
🎮 Level 5: Simulating completion...
🚫 Level 5: No reward (level_already_completed_today)
🎮 Level 6: Simulating completion...
🚫 Level 6: No reward (level_already_completed_today)
🎮 Level 7: Simulating completion...
✅ Level 7 completed! PARTIAL: 754 tokens
🎁 Game awarded 754 WISH tokens
🎮 Level 4: Simulating completion...
✅ Level 4 completed! PARTIAL: 1001 tokens
🎁 Game awarded 1001 WISH tokens
🎮 Level 4: Simulating completion...
🚫 Level 4: No reward (level_already_completed_today)
🎮 Level 7: Simulating completion...
🚫 Level 7: No reward (level_already_completed_today)
🎮 Level 6: Simulating completion...
✅ Level 6 completed! PARTIAL: 1158 tokens
🎁 Game awarded 1158 WISH tokens
🎮 Level 7: Simulating completion...
🚫 Level 7: No reward (level_already_completed_today)
🎮 Level 5: Simulating completion...
✅ Level 5 completed! PARTIAL: 1138 tokens
🎁 Game awarded 1138 WISH tokens
🎮 Level 6: Simulating completion...
🚫 Level 6: No reward (level_already_completed_today)
🎮 Level 5: Simulating completion...
🚫 Level 5: No reward (level_already_completed_today)
🎮 Level 8: Simulating completion...
✅ Level 8 completed! PARTIAL: 1550 tokens
🎁 Game awarded 1550 WISH tokens
🎮 Level 8: Simulating completion...
🚫 Level 8: No reward (level_already_completed_today)
🎮 Level 7: Simulating completion...
✅ Level 7 completed! PARTIAL: 1402 tokens
🎁 Game awarded 1402 WISH tokens
🎮 Level 9: Simulating completion...
✅ Level 9 completed! PARTIAL: 1869 tokens
🎁 Game awarded 1869 WISH tokens
🎮 Level 7: Simulating completion...
🚫 Level 7: No reward (level_already_completed_today)
🎮 Level 8: Simulating completion...
🚫 Level 8: No reward (level_already_completed_today)
🎮 Level 6: Simulating completion...
✅ Level 6 completed! PARTIAL: 899 tokens
🎁 Game awarded 899 WISH tokens
🎮 Level 6: Simulating completion...
🚫 Level 6: No reward (level_already_completed_today)
🎮 Level 9: Simulating completion...
🚫 Level 9: No reward (level_already_completed_today)
🎮 Level 8: Simulating completion...
✅ Level 8 completed! PARTIAL: 811 tokens
🎁 Game awarded 811 WISH tokens
🎮 Level 10: Simulating completion...
✅ Level 10 completed! PARTIAL: 1686 tokens
🎁 Game awarded 1686 WISH tokens
🎮 Level 8: Simulating completion...
🚫 Level 8: No reward (level_already_completed_today)
🎮 Level 7: Simulating completion...
✅ Level 7 completed! PARTIAL: 1205 tokens
🎁 Game awarded 1205 WISH tokens
🎮 Level 9: Simulating completion...
✅ Level 9 completed! PARTIAL: 1134 tokens
🎁 Game awarded 1134 WISH tokens
🎮 Level 7: Simulating completion...
🚫 Level 7: No reward (level_already_completed_today)
🎮 Level 10: Simulating completion...
🚫 Level 10: No reward (level_already_completed_today)
🎮 Level 9: Simulating completion...
🚫 Level 9: No reward (level_already_completed_today)
🎮 Level 9: Simulating completion...
🚫 Level 9: No reward (level_already_completed_today)
🏆 Grinder grinder_1 completed 10 levels with REAL rewards
📊 Treasury impact after grinder_1: -21999.999948 ETH
👤 Running casual player session: casual_1
🛒 Casual Spread Ammo: 750 ETH (ETH test mode)
💸 casual_1 sending 750 ETH to hot wallet for: Spread Ammo
🔢 Next nonce for casual_1: 0
✅ REAL blockchain transaction mined: 0x53ead27643dfad9af00188b135e0ad25d3bcb33f4ea6e1882ce50ae74340b9b3
⛽ Gas used: 21000
💸 casual_1 completed REAL ETH transfer: 750 ETH to hot wallet
🎮 Level 1: Simulating moderate completion (60%)...
✅ Level 1 completed! PARTIAL: 557 tokens
🎁 Game awarded 557 WISH tokens
🎮 Level 8: Simulating completion...
✅ Level 8 completed! PARTIAL: 1416 tokens
🎁 Game awarded 1416 WISH tokens
🎮 Level 10: Simulating completion...
✅ Level 10 completed! PARTIAL: 1103 tokens
🎁 Game awarded 1103 WISH tokens
🎮 Level 8: Simulating completion...
🚫 Level 8: No reward (level_already_completed_today)
🎮 Level 10: Simulating completion...
🚫 Level 10: No reward (level_already_completed_today)
🏆 Grinder grinder_1 completed 10 levels with REAL rewards
✅ Completed grinder session for grinder_1
🎮 Level 2: Simulating moderate completion (60%)...
✅ Level 2 completed! PARTIAL: 935 tokens
🎁 Game awarded 935 WISH tokens
🎮 Level 10: Simulating completion...
🚫 Level 10: No reward (level_already_completed_today)
🎮 Level 9: Simulating completion...
✅ Level 9 completed! PARTIAL: 1375 tokens
🎁 Game awarded 1375 WISH tokens
💰 Balance change: +750.000000 ETH (32749.999948 ETH total)
📈 Treasury inflow detected: +750.000000 ETH
🎮 Level 9: Simulating completion...
🚫 Level 9: No reward (level_already_completed_today)
🏆 Grinder grinder_1 completed 10 levels with REAL rewards
🎮 Level 3: Simulating moderate completion (60%)...
✅ Level 3 completed! PARTIAL: 782 tokens
🎁 Game awarded 782 WISH tokens
🏆 Grinder grinder_2 completed 10 levels with REAL rewards
✅ Completed grinder session for grinder_2
🏆 Grinder grinder_2 completed 10 levels with REAL rewards
🎮 Level 4: Simulating moderate completion (60%)...
✅ Level 4 completed! PARTIAL: 599 tokens
🎁 Game awarded 599 WISH tokens
🎮 Level 10: Simulating completion...
✅ Level 10 completed! PARTIAL: 1521 tokens
🎁 Game awarded 1521 WISH tokens
🎮 Level 10: Simulating completion...
🚫 Level 10: No reward (level_already_completed_today)
🎮 Level 5: Simulating moderate completion (60%)...
✅ Level 5 completed! PARTIAL: 834 tokens
🎁 Game awarded 834 WISH tokens
🏆 Grinder grinder_3 completed 10 levels with REAL rewards
📊 Treasury Drain Test Results:
   Stress Duration: 17284ms
   Users Participating: 10
   Initial Balance: 10000 ETH
   Final Balance: 32749.*********** ETH
   Total Drain: -22749.999948 ETH
   Drain Percentage: -227.50%
   Treasury Survived: ✅ YES
🏆 Grinder grinder_3 completed 10 levels with REAL rewards
✅ Completed grinder session for grinder_3
🎮 Level 6: Simulating moderate completion (60%)...
✅ Level 6 completed! PARTIAL: 1195 tokens
🎁 Game awarded 1195 WISH tokens
🎮 Level 7: Simulating moderate completion (60%)...
✅ Level 7 completed! PARTIAL: 1180 tokens
🎁 Game awarded 1180 WISH tokens
📊 Multi-Account Coordination Results:
   Coordination Duration: 18993ms
   Accounts Coordinated: 5
   Treasury Impact: Analyzing...
🎯 Casual player casual_1 completed session
🎯 Starting grinder session: grinder_2
⏳ Grinder grinder_2 waiting 3000ms to prevent server overload...
🎮 Level 1: Simulating completion...
✅ Level 1 completed! PARTIAL: 859 tokens
🎁 Game awarded 859 WISH tokens
🎮 Level 2: Simulating completion...
✅ Level 2 completed! PARTIAL: 461 tokens
🎁 Game awarded 461 WISH tokens
🎮 Level 3: Simulating completion...
✅ Level 3 completed! PARTIAL: 519 tokens
🎁 Game awarded 519 WISH tokens
🎮 Level 4: Simulating completion...
✅ Level 4 completed! PARTIAL: 822 tokens
🎁 Game awarded 822 WISH tokens
🎮 Level 5: Simulating completion...
✅ Level 5 completed! PARTIAL: 688 tokens
🎁 Game awarded 688 WISH tokens
🎮 Level 6: Simulating completion...
✅ Level 6 completed! PARTIAL: 1275 tokens
🎁 Game awarded 1275 WISH tokens
🎮 Level 7: Simulating completion...
✅ Level 7 completed! PARTIAL: 1115 tokens
🎁 Game awarded 1115 WISH tokens
🎮 Level 8: Simulating completion...
✅ Level 8 completed! PARTIAL: 1181 tokens
🎁 Game awarded 1181 WISH tokens
🎮 Level 9: Simulating completion...
✅ Level 9 completed! PARTIAL: 1011 tokens
🎁 Game awarded 1011 WISH tokens
🎮 Level 10: Simulating completion...
✅ Level 10 completed! PARTIAL: 1827 tokens
🎁 Game awarded 1827 WISH tokens
🏆 Grinder grinder_2 completed 10 levels with REAL rewards
📊 Treasury impact after grinder_2: -22749.999948 ETH
👤 Running casual player session: casual_2
🎮 Level 1: Simulating moderate completion (60%)...
✅ Level 1 completed! PARTIAL: 801 tokens
🎁 Game awarded 801 WISH tokens
🎮 Level 2: Simulating moderate completion (60%)...
✅ Level 2 completed! PARTIAL: 584 tokens
🎁 Game awarded 584 WISH tokens
🎮 Level 3: Simulating moderate completion (60%)...
✅ Level 3 completed! PARTIAL: 963 tokens
🎁 Game awarded 963 WISH tokens
🎮 Level 4: Simulating moderate completion (60%)...
✅ Level 4 completed! PARTIAL: 873 tokens
🎁 Game awarded 873 WISH tokens
🎮 Level 5: Simulating moderate completion (60%)...
✅ Level 5 completed! PARTIAL: 1097 tokens
🎁 Game awarded 1097 WISH tokens
🎮 Level 6: Simulating moderate completion (60%)...
✅ Level 6 completed! PARTIAL: 662 tokens
🎁 Game awarded 662 WISH tokens
🎮 Level 7: Simulating moderate completion (60%)...
✅ Level 7 completed! PARTIAL: 1218 tokens
🎁 Game awarded 1218 WISH tokens
🎯 Casual player casual_2 completed session
🎯 Starting grinder session: grinder_3
⏳ Grinder grinder_3 waiting 4500ms to prevent server overload...
🎮 Level 1: Simulating completion...
✅ Level 1 completed! PARTIAL: 609 tokens
🎁 Game awarded 609 WISH tokens
🎮 Level 2: Simulating completion...
✅ Level 2 completed! PARTIAL: 850 tokens
🎁 Game awarded 850 WISH tokens
🎮 Level 3: Simulating completion...
✅ Level 3 completed! PARTIAL: 700 tokens
🎁 Game awarded 700 WISH tokens
🎮 Level 4: Simulating completion...
✅ Level 4 completed! PARTIAL: 785 tokens
🎁 Game awarded 785 WISH tokens
🎮 Level 5: Simulating completion...
✅ Level 5 completed! PARTIAL: 1011 tokens
🎁 Game awarded 1011 WISH tokens
🎮 Level 6: Simulating completion...
✅ Level 6 completed! PARTIAL: 610 tokens
🎁 Game awarded 610 WISH tokens
🎮 Level 7: Simulating completion...
✅ Level 7 completed! PARTIAL: 1512 tokens
🎁 Game awarded 1512 WISH tokens
🎮 Level 8: Simulating completion...
✅ Level 8 completed! PARTIAL: 840 tokens
🎁 Game awarded 840 WISH tokens
🎮 Level 9: Simulating completion...
✅ Level 9 completed! PARTIAL: 1125 tokens
🎁 Game awarded 1125 WISH tokens
🎮 Level 10: Simulating completion...
✅ Level 10 completed! PARTIAL: 1279 tokens
🎁 Game awarded 1279 WISH tokens
🏆 Grinder grinder_3 completed 10 levels with REAL rewards
📊 Treasury impact after grinder_3: -22749.999948 ETH
📈 Sequential Grinding Test Results:
   Initial Balance: 10000 ETH
   Final Balance: 32749.*********** ETH
   Total Impact: -22749.999948 ETH
   Treasury Sustainable: ✅ YES
🛑 TreasuryMonitor stopped
📊 Monitored for 61016ms
💰 Final balance: 32749.*********** ETH
📈 Balance change: 22749.999948 ETH
🛑 TransactionTracker stopped
📊 Tracked 4 transactions in 61016ms
📊 Monitoring stopped
🔍 Queue health monitoring stopped
🔍 Running comprehensive validation...
🔍 Starting comprehensive validation...
💰 Validating treasury sustainability...
🔍 Treasury Status Debug:
🎨 Validating creator reward distribution...
🔍 Creator Analysis Debug:
🎮 Validating grinder behavior...
🔍 User Activity Debug:
🔗 Validating transaction integrity...
🔍 Transaction Data Debug:
⚖️ Validating economic balance...
🔧 Validating system stability...

🔍 VALIDATION RESULTS
==================================================
Overall Result: ❌ FAIL
Tests Passed: 13/17 (76.5%)
Critical Issues: 1
Warnings: 2

❌ CRITICAL ISSUES:
   1. Treasury Balance Change Reasonable: 227.50% (expected: ≤ 50%)

⚠️ WARNINGS:
   1. Creator Rewards Distributed: 0 ETH (expected: > 0 ETH)
   2. Grinder Users Present: 0 grinders (expected: > 0 grinders (when user activity is tracked))

💡 RECOMMENDATIONS:
   1. CRITICAL: Address all critical issues before production deployment
   2. Increase initial treasury funding or reduce reward payouts
   3. Review warning items for potential improvements

✅ Validation completed
📊 Test Report Generated
🧹 Cleaning up rate limiter resources...
📊 Final rate limiter status: {}
✅ Rate limiter cleanup completed
✅ Stress test completed
🧹 Cleaning up rate limiter resources...
📊 Final rate limiter status: {}
✅ Rate limiter cleanup completed

📊 STRESS TEST RESULTS
============================================================
📋 Test Summary:
   Total Duration: 65.044 seconds
   Users Simulated: 10
   Total Transactions: 4
   Test Completion: Early completion

💰 Treasury Analysis:
   Initial Balance: 10000 ETH
   Final Balance: 32749.*********** ETH
   Balance Change: 22749.*********** ETH
   Net Flow: 22749.*********** ETH
   Risk Level: low

🎨 Creator Reward Analysis:
   Total Creator Rewards: 0 ETH
   Average Reward per Environment: 0 ETH
   Reward Distribution Accuracy: 100%

📈 Sustainability Assessment:
   Is Sustainable: ✅ YES
   Risk Level: low
   Projected Runtime: undefined seconds

💡 Recommendations:
   1. Treasury balance stable
   2. Current tokenomics parameters appear sustainable
   3. Positive net flow - treasury is growing

✅ Stress test completed successfully
⏰ Test timeout reached