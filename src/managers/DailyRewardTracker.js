import { GAME_CONFIG } from '../config/gameConfig.js';

/**
 * DailyRewardTracker handles the daily reward limitations and tracking
 * Implements the new tokenomics system with daily max rewards per level
 */
export class DailyRewardTracker {
    constructor(userId = 'default') {
        this.userId = userId;

        // Daily tracking data - now per-user
        this.dailyData = {
            date: this.getCurrentDate(),
            completedLevels: new Set(), // Levels completed today by this user
            levelCompletionRates: {}, // Completion rates per level for this user
            dailyTokensEarned: 0,
            maxDailyTokens: 10000, // What the fuck is this? HARDCODING A LIMIT ON REWARDS IS NOT A SUBSTITUTE FOR FIXING THE TOKENOMICS!!!
            sessionTokensEarned: 0, // Track tokens earned in current session
            sessionStartTime: Date.now(),
            levelCompletionCount: {} // Track how many times each level was COMPLETED today
        };
        
        // Load saved data from localStorage
        this.loadFromStorage();
        
        // Ensure we're tracking the current day
        this.ensureCurrentDate();
    }
    
    /**
     * Get current date in YYYY-MM-DD format
     * @returns {string} Current date string
     */
    getCurrentDate() {
        return new Date().toISOString().split('T')[0];
    }
    
    /**
     * Ensure we're tracking data for the current date
     */
    ensureCurrentDate() {
        const today = this.getCurrentDate();
        if (this.dailyData.date !== today) {
            // Reset daily data for new day
            this.dailyData = {
                date: today,
                completedLevels: new Set(),
                levelCompletionRates: {},
                dailyTokensEarned: 0,
                maxDailyTokens: 100000
            };
            this.saveToStorage();
        }
    }
    
    /**
     * Check if a level can be rewarded today (not already completed)
     * @param {number} levelNumber - Level number to check
     * @returns {boolean} True if level can be rewarded
     */
    canEarnReward(levelNumber) {
        this.ensureCurrentDate();
        return !this.dailyData.completedLevels.has(levelNumber);
    }
    
    /**
     * Record level completion and reward
     * @param {number} levelNumber - Level number completed
     * @param {number} completionRate - Completion rate (0.0 - 1.0)
     * @param {number} rewardAmount - Token reward amount
     */
    recordLevelCompletion(levelNumber, completionRate, rewardAmount) {
        this.ensureCurrentDate();
        
        // Mark level as completed today
        this.dailyData.completedLevels.add(levelNumber);
        
        // Store completion rate
        this.dailyData.levelCompletionRates[levelNumber] = completionRate;
        
        // Add to daily tokens earned
        this.dailyData.dailyTokensEarned += rewardAmount;
        
        // Save to localStorage
        this.saveToStorage();
    }
    
    /**
     * Calculate max reward for a level based on exponential scaling
     * @param {number} levelNumber - Level number
     * @returns {number} Max reward amount
     */
    calculateMaxReward(levelNumber) {
        const baseReward = GAME_CONFIG.BASE_LEVEL_REWARD; // 1250 from gameConfig
        const exponentialMultiplier = Math.pow(1.6, Math.floor((levelNumber - 1) / 5));
        return Math.floor(baseReward * exponentialMultiplier);
    }
    
    /**
     * Calculate probabilistic reward with variance based on performance
     * @param {number} levelNumber - Level number
     * @param {object} levelData - Level data with totalEnemies and enemiesDefeated
     * @param {Array} activePowerUps - Currently active power-ups (optional)
     * @returns {number} Actual reward amount (rolled with variance)
     */
    calculateCompletionReward(levelNumber, levelData, activePowerUps = []) {
        const { totalEnemies, enemiesDefeated } = levelData;
        const completionPercentage = totalEnemies > 0 ? enemiesDefeated / totalEnemies : 0;

        // Get max theoretical reward for this level
        const maxReward = this.calculateMaxReward(levelNumber);

        // Apply probabilistic variance system
        const rewardData = this.calculateProbabilisticReward(maxReward, completionPercentage, activePowerUps);
        const actualReward = this.rollForReward(rewardData);

        return actualReward;
    }

    /**
     * Calculate probabilistic reward data with variance
     * @param {number} maxReward - Maximum possible reward
     * @param {number} completionPercentage - Performance score (0.0 to 1.0)
     * @param {Array} activePowerUps - Currently active power-ups
     * @returns {object} Reward calculation data
     */
    calculateProbabilisticReward(maxReward, completionPercentage, activePowerUps = []) {
        const config = GAME_CONFIG.REWARD_SYSTEM;

        // Determine variance based on performance
        let variance = config.BASE_VARIANCE;
        if (completionPercentage >= 1.0) {
            variance = config.PERFECT_SCORE_VARIANCE; // Perfect score = less variance
        } else if (completionPercentage < 0.5) {
            variance = config.POOR_SCORE_VARIANCE; // Poor score = more variance
        }

        // Power-ups reduce variance (more consistent rewards)
        const varianceReduction = Math.min(
            activePowerUps.length * config.POWER_UP_VARIANCE_REDUCTION,
            config.MAX_VARIANCE_REDUCTION
        );
        const finalVariance = Math.max(0.1, variance - varianceReduction);

        // Calculate reward range
        const minReward = Math.max(
            maxReward * config.MIN_REWARD_RATIO,
            maxReward * (1 - finalVariance)
        );
        const maxPossibleReward = maxReward * (1 + finalVariance * 0.5);

        return {
            maxReward: maxReward,
            minReward: minReward,
            maxPossibleReward: maxReward, // Max possible is now truly the max
            variance: finalVariance,
            expectedValue: maxReward * (1 - config.HOUSE_EDGE),
            completionPercentage: completionPercentage,
            activePowerUps: activePowerUps.length,
            maxRewardChance: config.MAX_REWARD_CHANCE,
            aboveAverageChance: config.ABOVE_AVERAGE_CHANCE,
            averageChance: config.AVERAGE_CHANCE,
            belowAverageChance: config.BELOW_AVERAGE_CHANCE,
            minRewardChance: config.MIN_REWARD_CHANCE
        };
    }

    /**
     * Roll for actual reward amount using variance
     * @param {object} rewardData - Data from calculateProbabilisticReward
     * @returns {number} Actual reward amount
     */
    rollForReward(rewardData) {
        const config = GAME_CONFIG.REWARD_SYSTEM;
        const roll = Math.random();

        // Bell curve distribution - no jackpot, max reward is truly the maximum
        if (roll < config.MIN_REWARD_CHANCE) {
            // 5% chance of minimum reward
            return Math.floor(rewardData.minReward);
        } else if (roll < config.MIN_REWARD_CHANCE + config.BELOW_AVERAGE_CHANCE) {
            // 20% chance of below average (30-50% of max)
            const range = rewardData.maxReward * 0.5 - rewardData.minReward;
            return Math.floor(rewardData.minReward + (Math.random() * range));
        } else if (roll < config.MIN_REWARD_CHANCE + config.BELOW_AVERAGE_CHANCE + config.AVERAGE_CHANCE) {
            // 40% chance of average (50-75% of max)
            const minRange = rewardData.maxReward * 0.5;
            const maxRange = rewardData.maxReward * 0.75;
            return Math.floor(minRange + (Math.random() * (maxRange - minRange)));
        } else if (roll < config.MIN_REWARD_CHANCE + config.BELOW_AVERAGE_CHANCE + config.AVERAGE_CHANCE + config.ABOVE_AVERAGE_CHANCE) {
            // 25% chance of above average (75-99% of max)
            const minRange = rewardData.maxReward * 0.75;
            const maxRange = rewardData.maxReward * 0.99;
            return Math.floor(minRange + (Math.random() * (maxRange - minRange)));
        } else {
            // 10% chance of max reward
            return Math.floor(rewardData.maxReward);
        }
    }
    
    /**
     * Check if daily token limit has been reached
     * @returns {boolean} True if daily limit reached
     */
    isDailyLimitReached() {
        this.ensureCurrentDate();
        return this.dailyData.dailyTokensEarned >= this.dailyData.maxDailyTokens;
    }
    
    /**
     * Get remaining daily token earning capacity
     * @returns {number} Remaining tokens that can be earned today
     */
    getRemainingDailyCapacity() {
        this.ensureCurrentDate();
        return Math.max(0, this.dailyData.maxDailyTokens - this.dailyData.dailyTokensEarned);
    }
    
    /**
     * Get daily progress data
     * @returns {object} Daily progress data
     */
    getDailyProgress() {
        this.ensureCurrentDate();
        return {
            date: this.dailyData.date,
            completedLevels: Array.from(this.dailyData.completedLevels),
            levelCompletionRates: { ...this.dailyData.levelCompletionRates },
            dailyTokensEarned: this.dailyData.dailyTokensEarned,
            maxDailyTokens: this.dailyData.maxDailyTokens,
            remainingCapacity: this.getRemainingDailyCapacity(),
            isLimitReached: this.isDailyLimitReached()
        };
    }
    
    /**
     * Load data from localStorage
     */
    loadFromStorage() {
        try {
            const storageKey = `dailyRewardTracker_${this.userId}`;
            const savedData = localStorage.getItem(storageKey);
            if (savedData) {
                const parsed = JSON.parse(savedData);
                this.dailyData = {
                    date: parsed.date || this.getCurrentDate(),
                    completedLevels: new Set(parsed.completedLevels || []),
                    levelCompletionRates: parsed.levelCompletionRates || {},
                    dailyTokensEarned: parsed.dailyTokensEarned || 0,
                    maxDailyTokens: parsed.maxDailyTokens || 100000
                };
            }
        } catch (error) {
            console.warn('Failed to load daily reward data from localStorage:', error);
        }
    }
    
    /**
     * Save data to localStorage
     */
    saveToStorage() {
        try {
            const dataToSave = {
                date: this.dailyData.date,
                completedLevels: Array.from(this.dailyData.completedLevels),
                levelCompletionRates: this.dailyData.levelCompletionRates,
                dailyTokensEarned: this.dailyData.dailyTokensEarned,
                maxDailyTokens: this.dailyData.maxDailyTokens
            };
            const storageKey = `dailyRewardTracker_${this.userId}`;
            localStorage.setItem(storageKey, JSON.stringify(dataToSave));
        } catch (error) {
            console.warn('Failed to save daily reward data to localStorage:', error);
        }
    }
    
    /**
     * Reset daily data (for testing)
     */
    reset() {
        this.dailyData = {
            date: this.getCurrentDate(),
            completedLevels: new Set(),
            levelCompletionRates: {},
            dailyTokensEarned: 0,
            maxDailyTokens: 100000
        };
        this.saveToStorage();
    }
}